<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // Define gates for all permissions
        Gate::before(function ($user, $ability) {
            // Check if user has the permission using our custom hasPermission method
            if ($user->hasPermission($ability)) {
                return true;
            }
            
            return null; // Let other gates handle it
        });
        
        // Specific gates for asset-fields
        Gate::define('asset-fields.view', function ($user) {
            return $user->hasPermission('asset-fields.view');
        });
        
        Gate::define('asset-fields.create', function ($user) {
            return $user->hasPermission('asset-fields.create');
        });
        
        Gate::define('asset-fields.edit', function ($user) {
            return $user->hasPermission('asset-fields.edit');
        });
        
        Gate::define('asset-fields.delete', function ($user) {
            return $user->hasPermission('asset-fields.delete');
        });
    }
}
