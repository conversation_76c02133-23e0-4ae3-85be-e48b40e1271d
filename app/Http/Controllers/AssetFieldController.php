<?php

namespace App\Http\Controllers;

use App\Models\AssetField;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AssetFieldController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-fields.view')) {
            abort(403, 'Bạn không có quyền xem danh sách field động');
        }

        if ($request->ajax()) {
            // Check if simple format is requested (for template management)
            if ($request->get('format') === 'simple') {
                $fields = AssetField::active()->ordered()->get();
                return response()->json($fields);
            }

            $query = AssetField::withCount('assetTemplates');

            // Filter by section if provided
            if ($request->has('section') && $request->section !== '') {
                $query->where('section', $request->section);
            }

            $fields = $query->orderBy('sort_order', 'asc')
                ->orderBy('name', 'asc')
                ->get();

            $fieldTypes = AssetField::getFieldTypes();
            $sectionOptions = AssetField::getSectionOptions();

            $data = $fields->map(function ($field) use ($fieldTypes, $sectionOptions) {
                $actions = '';

                if (Auth::user()->hasPermission('asset-fields.edit')) {
                    $actions .= '<button type="button" class="btn btn-sm btn-outline-warning me-1" onclick="editAssetField(' . $field->id . ')">
                        <i class="ri-edit-line"></i>
                    </button>';
                }

                if (Auth::user()->hasPermission('asset-fields.delete')) {
                    $actions .= '<button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAssetField(' . $field->id . ')">
                        <i class="ri-delete-bin-line"></i>
                    </button>';
                }

                $statusClass = $field->is_active ? 'bg-label-success' : 'bg-label-secondary';
                $statusText = $field->is_active ? 'Hoạt động' : 'Không hoạt động';
                $statusBadge = '<span class="badge ' . $statusClass . '">' . $statusText . '</span>';

                $requiredClass = $field->is_required ? 'bg-label-warning' : 'bg-label-secondary';
                $requiredText = $field->is_required ? 'Bắt buộc' : 'Không bắt buộc';
                $requiredBadge = '<span class="badge ' . $requiredClass . '">' . $requiredText . '</span>';

                // Section badge
                $fieldSection = $field->section ?: 'chung';
                $sectionClass = 'bg-label-info';
                if ($fieldSection === 'duong_su') {
                    $sectionClass = 'bg-label-primary';
                } elseif ($fieldSection === 'tai_san') {
                    $sectionClass = 'bg-label-success';
                } elseif ($fieldSection === 'chung') {
                    $sectionClass = 'bg-label-secondary';
                }
                $sectionText = $sectionOptions[$fieldSection] ?? 'Thông tin chung';
                $sectionBadge = '<span class="badge ' . $sectionClass . '">' . $sectionText . '</span>';

                return [
                    'id' => $field->id,
                    'name' => $field->name,
                    'label' => $field->label,
                    'type' => $field->type ?: 'text',
                    'type_formatted' => $fieldTypes[$field->type] ?? ($field->type ? 'Không xác định' : 'Text'),
                    'section' => $field->section ?: 'chung',
                    'section_badge' => $sectionBadge,
                    'description' => $field->description ?: '-',
                    'status_badge' => $statusBadge,
                    'required_badge' => $requiredBadge,
                    'templates_count' => $field->asset_templates_count,
                    'sort_order' => $field->sort_order,
                    'action' => $actions,
                    'is_active' => $field->is_active,
                    'is_required' => $field->is_required
                ];
            });

            return response()->json(['data' => $data]);
        }

        $fieldTypes = AssetField::getFieldTypes();
        $sectionOptions = AssetField::getSectionOptions();
        return view('asset-fields.index', compact('fieldTypes', 'sectionOptions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-fields.create')) {
            abort(403, 'Bạn không có quyền tạo field động');
        }

        $request->validate([
            'name' => 'required|string|max:255|unique:asset_fields,name',
            'label' => 'required|string|max:255',
            'type' => 'required|in:text,number,date,select,textarea,file,checkbox,radio',
            'section' => 'required|in:duong_su,tai_san,chung',
            'options' => 'nullable|array',
            'validation_rules' => 'nullable|array',
            'placeholder' => 'nullable|string',
            'help_text' => 'nullable|string',
            // 'is_required' => 'nullable|boolean', // Removed validation to avoid conflicts
            // 'is_active' => 'nullable|boolean',   // Will be handled in data processing
            'sort_order' => 'integer|min:0',
        ], [
            'section.required' => 'Vui lòng chọn loại field',
            'section.in' => 'Loại field không hợp lệ',
            'is_required.boolean' => 'Trường bắt buộc phải là true hoặc false',
            'is_active.boolean' => 'Trường hoạt động phải là true hoặc false',
        ]);

        try {
            $data = $request->all();



            // Convert boolean fields - simple conversion
            $data['is_required'] = filter_var($request->input('is_required', false), FILTER_VALIDATE_BOOLEAN);
            $data['is_active'] = filter_var($request->input('is_active', true), FILTER_VALIDATE_BOOLEAN);

            // Convert options to proper format if provided
            if ($request->has('options') && is_array($request->options)) {
                $options = [];
                foreach ($request->options as $option) {
                    if (isset($option['key']) && isset($option['value'])) {
                        $options[$option['key']] = $option['value'];
                    }
                }
                $data['options'] = $options;
            }

            $field = AssetField::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Field động đã được tạo thành công',
                'data' => $field,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo field động: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(AssetField $assetField)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-fields.view')) {
            abort(403, 'Bạn không có quyền xem field động');
        }

        return response()->json($assetField);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AssetField $assetField)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-fields.edit')) {
            abort(403, 'Bạn không có quyền sửa field động');
        }

        // Debug logging (only in development)
        if (config('app.debug')) {
            \Log::info('AssetField Update Request', [
                'asset_field_id' => $assetField->id,
                'request_data' => $request->all(),
                'request_method' => $request->method()
            ]);
        }

        $request->validate([
            'name' => 'required|string|max:255|unique:asset_fields,name,' . $assetField->id . ',id',
            'label' => 'required|string|max:255',
            'type' => 'required|in:text,number,date,select,textarea,file,checkbox,radio',
            'section' => 'required|in:duong_su,tai_san,chung',
            'options' => 'nullable|array',
            'validation_rules' => 'nullable|array',
            'placeholder' => 'nullable|string',
            'help_text' => 'nullable|string',
            // 'is_required' => 'nullable|boolean', // Removed validation to avoid conflicts
            // 'is_active' => 'nullable|boolean',   // Will be handled in data processing
            'sort_order' => 'integer|min:0',
        ], [
            'name.unique' => 'Tên field đã tồn tại, vui lòng chọn tên khác',
            'section.required' => 'Vui lòng chọn loại field',
            'section.in' => 'Loại field không hợp lệ',
            'is_required.boolean' => 'Trường bắt buộc phải là true hoặc false',
            'is_active.boolean' => 'Trường hoạt động phải là true hoặc false',
        ]);

        try {
            $data = $request->all();

            // Convert boolean fields - simple conversion
            $data['is_required'] = filter_var($request->input('is_required', false), FILTER_VALIDATE_BOOLEAN);
            $data['is_active'] = filter_var($request->input('is_active', true), FILTER_VALIDATE_BOOLEAN);

            // Convert options to proper format if provided
            if ($request->has('options') && is_array($request->options)) {
                $options = [];
                foreach ($request->options as $option) {
                    if (isset($option['key']) && isset($option['value'])) {
                        $options[$option['key']] = $option['value'];
                    }
                }
                $data['options'] = $options;
            }

            $assetField->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Field động đã được cập nhật thành công',
                'data' => $assetField,
            ]);
        } catch (\Exception $e) {
            \Log::error('AssetField Update Error', [
                'asset_field_id' => $assetField->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật field động: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AssetField $assetField)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-fields.delete')) {
            abort(403, 'Bạn không có quyền xóa field động');
        }

        // Check if field is used in any templates
        if ($assetField->assetTemplates()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể xóa field này vì đã được sử dụng trong template',
            ], 400);
        }

        try {
            $assetField->delete();

            return response()->json([
                'success' => true,
                'message' => 'Field động đã được xóa thành công',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa field động: ' . $e->getMessage(),
            ], 500);
        }
    }
}
