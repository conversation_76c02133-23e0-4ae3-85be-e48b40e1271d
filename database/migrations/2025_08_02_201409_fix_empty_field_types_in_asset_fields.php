<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Sửa các field có type rỗng hoặc null thành 'text'
        DB::table('asset_fields')
            ->whereNull('type')
            ->orWhere('type', '')
            ->update(['type' => 'text']);

        // Sửa các field có section rỗng hoặc null thành 'chung'
        DB::table('asset_fields')
            ->whereNull('section')
            ->orWhere('section', '')
            ->update(['section' => 'chung']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Không cần rollback vì đây là sửa dữ liệu lỗi
    }
};
