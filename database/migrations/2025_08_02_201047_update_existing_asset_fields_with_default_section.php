<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\AssetField;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Cập nhật các field hiện tại chưa có section thành 'chung'
        AssetField::whereNull('section')->update(['section' => 'chung']);

        // Cập nhật section_label cho các field chưa có
        AssetField::whereNull('section_label')->update(['section_label' => 'Thông tin chung']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Không cần rollback vì đây là cập nhật dữ liệu
    }
};
