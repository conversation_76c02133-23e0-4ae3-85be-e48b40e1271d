<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\User;

class AssetFieldsPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Tạo hoặc lấy role Admin
        $adminRole = Role::firstOrCreate([
            'name' => 'Admin',
            'guard_name' => 'web'
        ]);

        // Tạo hoặc lấy role Super Admin
        $superAdminRole = Role::firstOrCreate([
            'name' => 'Super Admin',
            'guard_name' => 'web'
        ]);

        // Danh sách permissions cho asset-fields
        $permissions = [
            'asset-fields.view',
            'asset-fields.create',
            'asset-fields.edit',
            'asset-fields.delete'
        ];

        // Tạo permissions nếu chưa tồn tại
        foreach ($permissions as $permissionName) {
            $permission = Permission::firstOrCreate([
                'name' => $permissionName,
                'guard_name' => 'web'
            ]);

            // Gán permission cho Admin role
            if (!$adminRole->hasPermission($permissionName)) {
                $adminRole->givePermissionTo($permission);
            }

            // Gán permission cho Super Admin role
            if (!$superAdminRole->hasPermission($permissionName)) {
                $superAdminRole->givePermissionTo($permission);
            }
        }

        // Cập nhật user admin để có role
        $adminUser = User::where('email', '<EMAIL>')->first();
        if ($adminUser && !$adminUser->role_id) {
            $adminUser->update(['role_id' => $adminRole->id]);
        }

        $superAdminUser = User::where('email', '<EMAIL>')->first();
        if ($superAdminUser && !$superAdminUser->role_id) {
            $superAdminUser->update(['role_id' => $superAdminRole->id]);
        }

        $this->command->info('Asset Fields permissions seeded successfully!');
    }
}
