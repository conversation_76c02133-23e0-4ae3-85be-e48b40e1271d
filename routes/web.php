<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\language\LanguageController;
use App\Http\Controllers\pages\HomePage;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\ContractTypeController;
use App\Http\Controllers\AssetFieldController;
use App\Http\Controllers\AssetTemplateController;
use App\Http\Controllers\LitigantController;

// Main Page Route
Route::middleware(['auth'])->group(function () {
  // locale
  Route::get('/lang/{locale}', [LanguageController::class, 'swap']);
  // home
  Route::get('/', [HomePage::class, 'index'])->name('pages-home');

  // Admin Routes
  Route::prefix('admin')->name('admin.')->group(function () {
    // User Management
    Route::resource('users', UserController::class);
    Route::get('users-data', [UserController::class, 'getData'])->name('users.data');
    Route::post('users/{user}/reset-password', [UserController::class, 'resetPassword'])->name('users.reset-password');

    // Role Management
    Route::resource('roles', RoleController::class);
    Route::get('roles-data', [RoleController::class, 'getData'])->name('roles.data');
  });

  // Documents Management Routes
  Route::prefix('documents')->name('documents.')->group(function () {
    Route::get('/', [DocumentController::class, 'index'])->name('index');
    Route::get('/wizard', [DocumentController::class, 'wizard'])->name('wizard');
    Route::post('/store-from-wizard', [DocumentController::class, 'storeFromWizard'])->name('store-from-wizard');
    Route::get('/{document}', [DocumentController::class, 'show'])->name('show');
    Route::delete('/{document}', [DocumentController::class, 'destroy'])->name('destroy');

    // AJAX routes
    Route::get('/ajax/templates-by-contract-type', [DocumentController::class, 'getTemplatesByContractType'])->name('ajax.templates-by-contract-type');
    Route::get('/ajax/template-fields', [DocumentController::class, 'getTemplateFields'])->name('ajax.template-fields');
    Route::get('/ajax/template-assets', [DocumentController::class, 'getTemplateAssets'])->name('ajax.template-assets');
    Route::get('/ajax/template-parties', [DocumentController::class, 'getTemplateParties'])->name('ajax.template-parties');
    Route::get('/ajax/search-parties', [DocumentController::class, 'searchParties'])->name('ajax.search-parties');
    Route::get('/ajax/search-assets', [DocumentController::class, 'searchAssets'])->name('ajax.search-assets');
  });

  // Contract Types Management
  Route::resource('contract-types', ContractTypeController::class);

  // Asset Fields Management
  Route::resource('asset-fields', AssetFieldController::class);

  // Asset Templates Management
  Route::resource('asset-templates', AssetTemplateController::class);

  // Enhanced Asset Templates route
  Route::get('asset-templates-enhanced', function() {
    $contractTypes = \App\Models\ContractType::active()->ordered()->get();
    return view('asset-templates.index-enhanced', compact('contractTypes'));
  })->name('asset-templates.enhanced');

  // Additional Asset Template routes
  Route::post('asset-templates/{assetTemplate}/import', [AssetTemplateController::class, 'import'])->name('asset-templates.import');
  Route::get('asset-templates/{assetTemplate}/preview', [AssetTemplateController::class, 'preview'])->name('asset-templates.preview');
  Route::get('asset-templates/{assetTemplate}/download-imported', [AssetTemplateController::class, 'downloadImportedFile'])->name('asset-templates.download-imported');
  Route::get('asset-templates/fields/sections', [AssetTemplateController::class, 'getFieldsBySections'])->name('asset-templates.fields.sections');
  Route::post('asset-templates/fields/suggestions', [AssetTemplateController::class, 'createFieldSuggestions'])->name('asset-templates.fields.suggestions');
  Route::post('asset-templates/fields/bulk-create', [AssetTemplateController::class, 'bulkCreateFields'])->name('asset-templates.fields.bulk-create');

  // Litigants Management
  Route::resource('litigants', LitigantController::class);
});
